FROM node:22-alpine AS build

# Enable corepack and pnpm
RUN corepack enable && corepack prepare pnpm@latest --activate

WORKDIR /usr/src/app

# Install deps early to cache them
COPY ./backend/pnpm-lock.yaml ./backend/package.json ./
RUN pnpm install

# # Start API in Dev Mode
# COPY ./.version ./backend/.version
# COPY ./backend/package.json ./backend/pnpm-lock.yaml ./backend/
# RUN yarn --cwd backend
# COPY ./backend/ ./backend/

EXPOSE 3000

# Start API in Dev Mode
CMD [ "pnpm", "start:api:dev" ]
