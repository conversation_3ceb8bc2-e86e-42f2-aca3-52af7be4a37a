import {
  Injectable,
  NotFoundException,
  ConflictException,
} from '@nestjs/common';
import { DriverVehicleRepository } from '../../repositories/driver-vehicle.repository';
import { DriverVehicleDocumentRepository } from '../../repositories/driver-vehicle-document.repository';
import { CashfreeVehicleVerificationService } from '@shared/shared/common/verifications/cashfree/cashfree-vehicle-verification.service';
import { DriverVehicleDocument } from '@shared/shared/repositories/models/driverVehicleDocument.model';
import { VehicleDocumentRepository } from '@shared/shared/repositories/vehicle-document.repository';
import { UserOnboardingService } from '../user-onboarding/user-onboarding.service';
import { OnboardingStep } from '@shared/shared/repositories/models/userOnboard.model';
import { UserProfileService } from '../user-profile/user-profile.service';

@Injectable()
export class DriverVehicleDocumentService {
  constructor(
    private readonly driverVehicleRepository: DriverVehicleRepository,
    private readonly driverVehicleDocumentRepository: DriverVehicleDocumentRepository,
    private readonly cashfreeVehicleVerificationService: CashfreeVehicleVerificationService,
    private readonly vehicleDocumentRepository: VehicleDocumentRepository,
    private readonly userOnboardingService: UserOnboardingService,
    private readonly userProfileService: UserProfileService,
  ) {}

  /**
   * Finds a driver vehicle document by driverVehicleId
   */
  async findByDriverVehicleId(
    driverVehicleId: string,
  ): Promise<DriverVehicleDocument[]> {
    return this.driverVehicleDocumentRepository.findMany({
      where: { driverVehicleId },
    });
  }
  /**
   * Verifies a driver vehicle with Cashfree and creates/updates the document if valid
   * @param driverVehicleId
   * @returns the created/updated DriverVehicleDocument
   */
  async verifyAndSaveDocument(driverVehicleId: string): Promise<any> {
    // Find the driver vehicle
    const driverVehicle =
      await this.driverVehicleRepository.findDriverVehicleById(driverVehicleId);
    if (!driverVehicle) throw new NotFoundException('Driver vehicle not found');
    if (!driverVehicle.vehicleNumber)
      throw new ConflictException(
        'Vehicle number is required for verification',
      );

    //need to rewrite with country specific vehicle document
    const vehicleDocument =
      await this.vehicleDocumentRepository.findOneByIdentifier(
        'vehicle_registration',
      );
    if (!vehicleDocument) {
      throw new NotFoundException('Vehicle document not found');
    }
    const userProfile = await this.userProfileService.findDriverProfileByUserId(
      driverVehicle.userProfileId,
    );
    if (!userProfile) {
      throw new NotFoundException(
        `User profile for user ID ${driverVehicle.userProfileId} not found`,
      );
    }
    // Call Cashfree verification
    const verificationResult =
      await this.cashfreeVehicleVerificationService.verifyVehicle(
        driverVehicleId + new Date().getTime(), // Unique reference ID
        driverVehicle.vehicleNumber,
      );

    if (verificationResult.status !== 'VALID') {
      throw new ConflictException(
        'Vehicle verification failed or is not valid',
      );
    }

    // Check if NOC is required by comparing owner name with user profile name
    const ownerName = verificationResult.owner?.toLowerCase().trim();
    const userFullName = `${userProfile.firstName} ${userProfile.lastName}`
      .toLowerCase()
      .trim();

    if (ownerName && ownerName !== userFullName) {
      // Update the driver vehicle to mark NOC as required
      await this.driverVehicleRepository.updateDriverVehicle(driverVehicle.id, {
        isNocRequired: true,
      });
    }

    // Upsert driver vehicle document
    const docData = {
      driverVehicleId: driverVehicle.id,
      details: verificationResult,
      vehicleDocumentId: vehicleDocument.id, //
    };

    // Try to find existing document
    const existing = await this.driverVehicleDocumentRepository.findOne({
      where: { driverVehicleId: driverVehicle.id },
    });
    let result;
    if (existing) {
      result = await this.driverVehicleDocumentRepository.update({
        where: { id: existing.id },
        data: docData,
      });
    } else {
      result = await this.driverVehicleDocumentRepository.create(docData);

      await this.userOnboardingService.updateOrCreateOnboardingStepByUserAndRole(
        userProfile.id,
        userProfile.roleId,
        OnboardingStep.VEHICLE_DOCUMENTS_VERIFICATION,
      );
    }
    return result;
  }

  /**
   * Upload NOC document for a driver vehicle
   * @param driverVehicleId
   * @param documentUrl
   * @returns the created/updated DriverVehicleDocument
   */
  async uploadNocDocument(
    driverVehicleId: string,
    documentUrl: string,
  ): Promise<DriverVehicleDocument> {
    // Find the driver vehicle
    const driverVehicle =
      await this.driverVehicleRepository.findDriverVehicleById(driverVehicleId);
    if (!driverVehicle) {
      throw new NotFoundException('Driver vehicle not found');
    }

    // Find NOC vehicle document type
    const nocDocument =
      await this.vehicleDocumentRepository.findOneByIdentifier('noc');
    if (!nocDocument) {
      throw new NotFoundException('NOC document type not found');
    }

    // Check if NOC document already exists for this driver vehicle
    const existingNocDocument =
      await this.driverVehicleDocumentRepository.findOne({
        where: {
          driverVehicleId,
          vehicleDocumentId: nocDocument.id,
        },
      });

    const docData = {
      driverVehicleId,
      vehicleDocumentId: nocDocument.id,
      documentUrl,
      details: {
        uploadedAt: new Date(),
        documentType: 'noc',
      },
    };

    let result: DriverVehicleDocument;
    if (existingNocDocument) {
      // Update existing NOC document
      result = await this.driverVehicleDocumentRepository.update({
        where: { id: existingNocDocument.id },
        data: docData,
      });
    } else {
      // Create new NOC document
      result = await this.driverVehicleDocumentRepository.create(docData);
    }

    return result;
  }
}
