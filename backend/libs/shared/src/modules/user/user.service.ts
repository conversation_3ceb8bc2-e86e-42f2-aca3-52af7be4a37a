import { Injectable, NotFoundException } from '@nestjs/common';
import { UserRepository } from '../../repositories/user.repository';
import { RoleRepository } from '../../repositories/role.repository';
import { AwsFileUploadService } from '@shared/shared/common/file-upload/aws-s3/aws-file-upload.servie';

@Injectable()
export class UserService {
  constructor(
    private readonly userRepository: UserRepository,
    private readonly roleRepository: RoleRepository,
    private readonly awsFileUploadService: AwsFileUploadService,
  ) {}

  /**
   * Find a user by id, including userRoles (driver), userProfile, and language.
   */
  async findDriverUserById(userId: string) {
    // Find the driver role
    const driverRole = await this.roleRepository.findByName('driver');
    if (!driverRole) throw new NotFoundException('Driver role not found');
    const driverRoleId = driverRole.id;
    const user: any = await this.userRepository.findOne({
      where: {
        id: userId,
        userRoles: { some: { roleId: driverRoleId } },
      },
      include: {
        userProfiles: {
          take: 1,
          where: { roleId: driverRoleId },
          include: {
            language: true,
          },
        },
        driverVehicles: {
          include: {
            vehicle: true,
          },
        },
      },
    });
    if (!user) throw new NotFoundException('User with driver role not found');
    let userProfile = undefined;
    if (user?.userProfiles?.length) {
      userProfile = { ...user.userProfiles[0] };
      if (userProfile.profilePictureUrl) {
        userProfile.profilePictureUrl =
          await this.awsFileUploadService.getSignedUrl(
            userProfile.profilePictureUrl,
          );
      }
    }
    user.userProfiles = undefined;
    return {
      ...user,
      userProfile,
    };
  }
}
