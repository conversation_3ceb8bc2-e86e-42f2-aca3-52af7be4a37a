import { Injectable } from '@nestjs/common';
import { BaseRepository } from './base.repository';
import { PrismaService } from '../database/prisma/prisma.service';
import { UserProfile } from './models/userProfile.model';

@Injectable()
export class UserProfileRepository extends BaseRepository<UserProfile> {
  protected readonly modelName = 'userProfile';

  constructor(prisma: PrismaService) {
    super(prisma);
  }

  /**
   * Create a new user profile.
   */
  async createUserProfile(
    userId: string,
    roleId: string,
    data: Omit<
      UserProfile,
      'id' | 'userId' | 'roleId' | 'createdAt' | 'updatedAt' | 'deletedAt'
    >,
  ): Promise<UserProfile> {
    return this.create({ userId, roleId, ...data });
  }

  /**
   * Update a user profile by userId and roleId.
   */
  async updateUserProfile(
    userId: string,
    roleId: string,
    data: Partial<
      Omit<
        UserProfile,
        'id' | 'userId' | 'roleId' | 'createdAt' | 'updatedAt' | 'deletedAt'
      >
    >,
  ): Promise<UserProfile> {
    return this.update({
      where: { userId, roleId },
      data,
    });
  }

  async updateUserProfileById(
    id: string,
    data: Partial<
      Omit<
        UserProfile,
        'id' | 'userId' | 'roleId' | 'createdAt' | 'updatedAt' | 'deletedAt'
      >
    >,
  ): Promise<UserProfile> {
    return this.update({
      where: { id },
      data,
    });
  }

  /**
   * Get a user profile by userId and roleId.
   */
  async getOneByUserIdAndRoleId(
    userId: string,
    roleId: string,
  ): Promise<UserProfile | null> {
    return this.findOne({ where: { userId, roleId } });
  }

  /**
   * Create or update a user profile by userId and roleId.
   * If a profile exists for the user and role, it will be updated; otherwise, a new one is created.
   */
  async createOrUpdateUserProfile(
    userId: string,
    roleId: string,
    data: Partial<
      Omit<UserProfile, 'id' | 'userId' | 'roleId' | 'createdAt' | 'updatedAt'>
    >,
  ): Promise<UserProfile> {
    // Ensure all required fields for creation are present
    const requiredFields = ['firstName', 'lastName', 'referralCode'];
    for (const field of requiredFields) {
      if (!(field in data) || data[field as keyof typeof data] === undefined) {
        throw new Error(`Missing required field: ${field}`);
      }
    }
    const createObj: any = {
      userId,
      roleId,
      firstName: data.firstName!,
      lastName: data.lastName!,
      referralCode: data.referralCode!,
    };
    if (data.cityId !== undefined) createObj.cityId = data.cityId;
    if (data.profilePictureUrl !== undefined)
      createObj.profilePictureUrl = data.profilePictureUrl;
    if (data.gender !== undefined) createObj.gender = data.gender;
    if (data.dob !== undefined) createObj.dob = data.dob;
    return this.upsert({ userId_roleId: { userId, roleId } }, createObj, data);
  }

  async findOrCreateByUserIdAndRoleId(
    userId: string,
    roleId: string,
  ): Promise<UserProfile> {
    const existing = await this.getOneByUserIdAndRoleId(userId, roleId);
    if (existing) {
      return existing;
    }
    return this.create({ userId, roleId });
  }

}
