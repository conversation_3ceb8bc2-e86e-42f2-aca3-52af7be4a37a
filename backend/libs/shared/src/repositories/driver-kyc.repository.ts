import { Injectable } from '@nestjs/common';
import { BaseRepository } from './base.repository';
import { DriverKyc, KycStatus } from './models/driverKyc.model';
import { PrismaService } from '../database/prisma/prisma.service';

@Injectable()
export class DriverKycRepository extends BaseRepository<DriverKyc> {
  protected readonly modelName = 'driverKyc';

  constructor(prisma: PrismaService) {
    super(prisma);
  }

  /**
   * Create a new driver KYC document
   */
  async createDriverKyc(
    data: Omit<DriverKyc, 'id' | 'createdAt' | 'updatedAt' | 'deletedAt'>,
  ): Promise<DriverKyc> {
    return this.create(data);
  }

  /**
   * Find all driver KYC documents with relations
   */
  async findAllDriverKycs(): Promise<DriverKyc[]> {
    return this.findMany({
      include: {
        kycDocument: {
          include: {
            country: true,
          },
        },
        userProfile: {
          include: {
            user: true,
          },
        },
      },
    });
  }

  /**
   * Find driver KYC document by ID with relations
   */
  async findDriverKycById(id: string): Promise<DriverKyc | null> {
    return this.findById(id, {
      include: {
        kycDocument: {
          include: {
            country: true,
          },
        },
        userProfile: {
          include: {
            user: true,
          },
        },
      },
    });
  }

  /**
   * Find driver KYC documents by user profile ID
   */
  async findDriverKycsByUserProfileId(
    userProfileId: string,
  ): Promise<DriverKyc[]> {
    return this.findMany({
      where: {
        userProfileId,
      },
      include: {
        kycDocument: {
          include: {
            country: true,
          },
        },
        userProfile: {
          include: {
            user: true,
          },
        },
      },
    });
  }

  /**
   * Find driver KYC documents by status
   */
  async findDriverKycsByStatus(status: KycStatus): Promise<DriverKyc[]> {
    return this.findMany({
      where: {
        status,
      },
      include: {
        kycDocument: {
          include: {
            country: true,
          },
        },
        userProfile: {
          include: {
            user: true,
          },
        },
      },
    });
  }

  /**
   * Find driver KYC document by user profile ID and KYC document ID
   */
  async findDriverKycByUserAndDocument(
    userProfileId: string,
    kycDocumentId: string,
  ): Promise<DriverKyc | null> {
    return this.findOne({
      where: {
        userProfileId,
        kycDocumentId,
      },
      include: {
        kycDocument: {
          include: {
            country: true,
          },
        },
        userProfile: {
          include: {
            user: true,
          },
        },
      },
    });
  }

  /**
   * Update driver KYC document
   */
  async updateDriverKyc(
    id: string,
    data: Partial<DriverKyc>,
  ): Promise<DriverKyc> {
    return this.updateById(id, data);
  }

  /**
   * Update driver KYC status
   */
  async updateDriverKycStatus(
    id: string,
    status: KycStatus,
    rejectionNote?: string,
  ): Promise<DriverKyc> {
    const updateData: Partial<DriverKyc> = { status };
    if (rejectionNote !== undefined) {
      updateData.rejectionNote = rejectionNote;
    }
    return this.updateById(id, updateData);
  }

  /**
   * Soft delete driver KYC document
   */
  async deleteDriverKyc(id: string): Promise<DriverKyc> {
    return this.softDeleteById(id);
  }

  /**
   * Get paginated driver KYC documents
   */
  async paginateDriverKycs(
    page: number,
    limit: number,
    filters?: {
      userProfileId?: string;
      status?: KycStatus;
      kycDocumentId?: string;
    },
  ) {
    const where: any = {};

    if (filters?.userProfileId) {
      where.userProfileId = filters.userProfileId;
    }

    if (filters?.status) {
      where.status = filters.status;
    }

    if (filters?.kycDocumentId) {
      where.kycDocumentId = filters.kycDocumentId;
    }

    return this.paginate(page, limit, {
      where,
      include: {
        kycDocument: {
          include: {
            country: true,
          },
        },
        userProfile: {
          include: {
            user: true,
          },
        },
      },
      orderBy: {
        createdAt: 'desc',
      },
    });
  }
}
