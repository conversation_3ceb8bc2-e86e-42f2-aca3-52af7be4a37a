model AuthCredential {
  id           String    @id @default(uuid()) @map("id") @db.Uuid
  type         String    @map("type") // PHONE, GOOGLE, APPLE
  identifier   String    @map("identifier") // phone number for PHONE, sub for OAuth providers
  userId       String    @map("user_id") @db.Uuid
  metadata     Json?     @map("metadata") // Store additional data like email from OAuth
  accessToken  String?   @map("access_token")
  refreshToken String?   @map("refresh_token")
  expiresAt    DateTime? @map("expires_at") @db.Timestamptz
  createdAt    DateTime  @default(now()) @map("created_at")
  updatedAt    DateTime  @updatedAt @map("updated_at")
  deletedAt    DateTime? @map("deleted_at") @db.Timestamptz

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([type, identifier], name: "uq_auth_credential_type_identifier")
  @@index([type, identifier], name: "idx_auth_credential_type_identifier")
  @@index([userId], name: "idx_auth_credential_user_id")
  
  @@map("auth_credentials")
}
