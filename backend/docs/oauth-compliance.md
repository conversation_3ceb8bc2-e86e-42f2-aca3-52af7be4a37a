# OAuth Compliance and Apple Masked Email Handling

## Overview

This document describes the OAuth implementation improvements made to handle Apple's "Hide My Email" feature and ensure OAuth compliance for both Google and Apple Sign-In.

## Key Issues Addressed

### 1. Apple Masked Email Changes
**Problem**: Apple's "Hide My Email" feature can generate different masked emails (`@privaterelay.appleid.com`) for the same user over time, leading to duplicate accounts.

**Solution**: 
- Primary user identification via OAuth provider's unique `sub` (subject) ID
- Email change detection and tracking
- Email history maintenance in credential metadata

### 2. OAuth Provider ID Priority
**Problem**: Previous implementation relied primarily on email for user lookup, which is unreliable for Apple users.

**Solution**:
- OAuth provider ID (`sub`) is now the primary identifier
- Email lookup is used as fallback only
- Proper linking of OAuth accounts to existing users

### 3. Email Verification Status
**Problem**: OAuth-provided emails should be considered pre-verified.

**Solution**:
- OAuth emails are automatically marked as verified (`emailVerifiedAt`)
- Differentiation between OAuth and manually entered emails

## Implementation Details

### User Registration Flow

```typescript
async registerWithOAuth(provider, userData, role) {
  // 1. Try to find user by OAuth provider ID (most reliable)
  let existingCredential = await findByTypeAndIdentifier(provider, userData.sub);
  
  if (existingCredential?.user) {
    // Handle potential email changes (especially for Apple)
    await handleOAuthEmailUpdate(user, existingCredential, userData, provider);
    return user;
  }
  
  // 2. Fallback: find by email
  let user = await findByEmail(userData.email);
  
  if (user) {
    // Link OAuth account to existing user
    await linkOAuthToExistingUser(user, provider, userData);
    return user;
  }
  
  // 3. Create new user
  return createNewOAuthUser(provider, userData, role);
}
```

### Apple Email Change Handling

```typescript
async handleOAuthEmailUpdate(user, credential, userData, provider) {
  const currentEmail = credential.metadata?.currentEmail || user.email;
  
  if (currentEmail !== userData.email) {
    // Validate email uniqueness
    await validateEmailUniqueness(userData.email, user.id);
    
    // Update user's primary email
    await updateUserEmail(user.id, userData.email);
    
    // Track email history
    const emailHistory = [...(credential.metadata?.emailHistory || []), userData.email];
    
    await updateMetadata(credential.id, {
      currentEmail: userData.email,
      emailHistory,
      lastEmailUpdate: new Date(),
      isPrivateEmail: userData.isPrivateEmail,
      realUserStatus: userData.realUserStatus,
    });
  }
}
```

### Metadata Structure

OAuth credentials now store comprehensive metadata:

```json
{
  "currentEmail": "<EMAIL>",
  "emailHistory": [
    "<EMAIL>",
    "<EMAIL>"
  ],
  "provider": "APPLE",
  "isPrivateEmail": true,
  "realUserStatus": "likelyReal",
  "lastEmailUpdate": "2025-07-28T10:30:00Z",
  "registeredAt": "2025-07-28T10:00:00Z",
  "name": "John Doe"
}
```

## Database Schema Updates

### Indexes Added
- `idx_users_email_not_null`: Faster email lookups
- `idx_auth_credentials_metadata_email`: GIN index for metadata email queries
- `idx_auth_credentials_apple`: Partial index for Apple credentials
- `idx_auth_credentials_google`: Partial index for Google credentials

### Constraints
- Unique constraint on `(type, identifier)` in auth_credentials ensures no duplicate OAuth accounts
- Email uniqueness is enforced at application level with proper error handling

## Security Considerations

### Email Validation
- Email uniqueness is validated before updates
- Proper error handling for email conflicts
- Logging of suspicious email transitions

### Apple-Specific Security
- Detection of private relay emails
- Tracking of `real_user_status` from Apple
- Email history cleanup for privacy compliance

### Audit Trail
- Comprehensive logging of OAuth operations
- Email change tracking
- Provider-specific metadata storage

## Testing

### Test Coverage
- Apple masked email changes
- New user creation
- Account linking scenarios
- Email conflict handling
- Google OAuth flow

### Manual Testing Scenarios
1. **Apple First Login**: User signs in with Apple for the first time
2. **Apple Email Change**: Apple changes user's masked email
3. **Account Linking**: User with existing email account signs in with Apple
4. **Email Conflicts**: Attempt to use email already associated with another user
5. **Google Login**: Standard Google OAuth flow

## Monitoring and Debugging

### Logging
All OAuth operations are logged with:
- Provider type (APPLE/GOOGLE)
- User ID
- Email changes
- Error conditions

### Key Log Messages
- `[OAuth] Email changed for APPLE user {userId}: {oldEmail} -> {newEmail}`
- `[OAuth] Apple private email detected for user {userId}`
- `[OAuth] Creating new user for {provider} with email: {email}`

## API Endpoints

### OAuth Login
```
POST /auth/oauth/login
Headers: x-app-type: rider|driver
Body: {
  "provider": "APPLE|GOOGLE",
  "accessToken": "oauth_token_here"
}
```

### Response
```json
{
  "success": true,
  "message": "OAuth login successful",
  "data": {
    "accessToken": "jwt_token",
    "refreshToken": "refresh_token",
    "expiresIn": 3600,
    "isProfileUpdated": true,
    "isPolicyAllowed": false,
    "isEmailVerified": true,
    "isPhoneVerified": false,
    "email": "<EMAIL>",
    "phone": null
  }
}
```

## Future Improvements

1. **Email Cleanup**: Implement periodic cleanup of old email history for privacy
2. **Enhanced Validation**: Add more sophisticated Apple email transition validation
3. **Metrics**: Add metrics for OAuth success/failure rates
4. **Rate Limiting**: Implement rate limiting for OAuth endpoints
5. **Account Merging**: Advanced account merging capabilities for complex scenarios

## Compliance Status

✅ **Google OAuth**: Fully compliant
- Proper ID token verification
- Correct audience validation
- Secure user data extraction

✅ **Apple OAuth**: Fully compliant with masked email support
- Identity token verification
- Masked email change handling
- Private relay email detection
- Real user status tracking

✅ **General OAuth**: Compliant with best practices
- Provider ID as primary identifier
- Secure token handling
- Proper error responses
- Comprehensive audit logging
