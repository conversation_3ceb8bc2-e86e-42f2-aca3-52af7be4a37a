import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsNotEmpty, IsUrl } from 'class-validator';

export class UploadNocDto {
  @ApiProperty({
    example: 'https://s3.amazonaws.com/bucket/uploads/noc-document.pdf',
    description: 'S3 URL of the uploaded NOC document',
  })
  @IsString()
  @IsNotEmpty({ message: 'Document URL is required' })
  @IsUrl({}, { message: 'Document URL must be a valid URL' })
  documentUrl!: string;
}
