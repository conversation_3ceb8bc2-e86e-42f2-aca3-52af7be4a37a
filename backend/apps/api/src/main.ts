import { NestFactory } from '@nestjs/core';
import { ApiModule } from './api.module';
import { AppConfigService } from '@shared/shared/config';
import helmet from 'helmet';
import { Logger, ValidationPipe, VersioningType } from '@nestjs/common';
import { setupSwagger } from './docs/swagger';

async function bootstrap() {
  const app = await NestFactory.create(ApiModule, {
    cors: true,
  });

  const configService = app.get(AppConfigService);
  const port = configService.coreApiPort;
  const host = configService.coreApiHost;
  const logger = new Logger('Bootstrap');

  app.enableCors({
    origin: configService.corsOrigins.split(',').map((origin) => origin.trim()),
    methods: 'GET,HEAD,PUT,PATCH,POST,DELETE,OPTIONS',
    credentials: true,
  });

  app.use(helmet());

  // Enable versioning
  app.enableVersioning({
    type: VersioningType.URI,
    prefix: 'v',
    defaultVersion: '1',
  });

  app.setGlobalPrefix('api');

  app.useGlobalPipes(
    new ValidationPipe({
      whitelist: true,
      transform: true,
      forbidNonWhitelisted: true,
      transformOptions: {
        enableImplicitConversion: true,
      },
    }),
  );

  // Setup Swagger documentation
  setupSwagger(app);

  await app.listen(port, host);
  logger.log(`Tuxi Core api is running on ${await app.getUrl()}`);
  logger.log(`Swagger documentation available at ${await app.getUrl()}/docs`);
}
void bootstrap();
