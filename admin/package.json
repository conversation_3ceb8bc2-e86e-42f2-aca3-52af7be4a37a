{"name": "admin", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@hookform/resolvers": "^5.1.1", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-slot": "^1.2.3", "@tanstack/react-query": "^5.83.0", "axios": "^1.10.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.525.0", "next": "15.3.5", "nextjs-toploader": "^3.8.16", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.60.0", "sonner": "^2.0.6", "tailwind-merge": "^3.3.1", "zod": "^4.0.5", "zustand": "^5.0.6"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.5", "tailwindcss": "^4", "tw-animate-css": "^1.3.5", "typescript": "^5"}, "packageManager": "pnpm@9.15.5+sha512.845196026aab1cc3f098a0474b64dfbab2afe7a1b4e91dd86895d8e4aa32a7a6d03049e2d0ad770bbe4de023a7122fb68c1a1d6e0d033c7076085f9d5d4800d4"}